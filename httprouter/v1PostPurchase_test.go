package httprouter

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	evergentproxy "github.com/foxcorp-product/commerce-evergentproxy/client"
	plans "github.com/foxcorp-product/commerce-plan/client"

	"github.com/foxcorp-product/commerce-purchase/braze"
	"github.com/foxcorp-product/commerce-purchase/config"
	"github.com/foxcorp-product/commerce-purchase/featureflag"
	"github.com/go-chi/chi/v5"

	entitlement "github.com/foxcorp-product/commerce-entitlement/client"
	accountV2 "github.com/foxcorp-product/commerce-purchase/clients/accountV2"
	accountV2Models "github.com/foxcorp-product/commerce-purchase/clients/accountV2/models"
	receiptverify "github.com/foxcorp-product/commerce-purchase/clients/receiptverify"
	receiptverifyModels "github.com/foxcorp-product/commerce-purchase/clients/receiptverify/models"

	"github.com/foxcorp-product/commerce-purchase/models"
	"github.com/foxcorp-product/commerce-purchase/purchase"
	"github.com/foxcorp-product/commerce-purchase/utils"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/jwtdecoder"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/response"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/foxcorp-product/entitlement-sdk/x"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestV1PurchaseHandler_verifyReceipt(t *testing.T) {
	t.Run("Test verify receipt with transaction", func(t *testing.T) {
		mockReceiptVerifyClient := receiptverify.NewClientMock()
		req := &models.V1PostPurchaseRequest{
			Receipt:               "receipt",
			Label:                 "tizen",
			AppServiceId:          "service",
			IsAppstoreTransaction: utils.ToPtr(true),
		}

		apikey := "apikey"

		mockReceiptVerifyClient.Mock.On("PostReceiptverifyValidateAppstoreTransaction", mock.Anything, mock.Anything, mock.Anything).Return(&receiptverifyModels.V1PostReceiptverifyResponse{
			TransactionId: "app-storetransaction",
		}, 200, nil)

		h := V1PurchaseHandler{
			d: &V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerifyClient,
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
		}

		want := &receiptverifyModels.V1PostReceiptverifyResponse{
			TransactionId: "app-storetransaction",
		}
		_, _, err := h.verifyReceipt(context.Background(), req, apikey)
		assert.NoError(t, err)
		assert.Equal(t, want, want)

		mockReceiptVerifyClient.AssertExpectations(t)

		expectBody := receiptverify.PostReceiptverifyValidateAppstoreTransactionBody{
			AppServiceId: req.AppServiceId,
			Receipt:      req.Receipt,
		}
		expectHeaders := receiptverify.PostReceiptverifyValidateAppstoreTransactionHeaderParams{
			XApiKey: &apikey,
		}

		mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverifyValidateAppstoreTransaction", mock.Anything, expectBody, expectHeaders)
		mockReceiptVerifyClient.AssertNotCalled(t, "PostReceiptverify", mock.Anything, mock.Anything, mock.Anything)
	})

	t.Run("Test verify receipt without transaction", func(t *testing.T) {
		mockReceiptVerifyClient := receiptverify.NewClientMock()
		req := &models.V1PostPurchaseRequest{
			Receipt:               "receipt",
			Label:                 "tizen",
			AppServiceId:          "service",
			IsAppstoreTransaction: utils.ToPtr(false),
		}

		apikey := "apikey"

		mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(&receiptverifyModels.V1PostReceiptverifyResponse{
			TransactionId: "app-storetransaction",
		}, 200, nil)

		h := V1PurchaseHandler{
			d: &V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerifyClient,
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
		}

		want := &receiptverifyModels.V1PostReceiptverifyResponse{
			TransactionId: "app-storetransaction",
		}
		_, _, err := h.verifyReceipt(context.Background(), req, apikey)
		assert.NoError(t, err)
		assert.Equal(t, want, want)

		mockReceiptVerifyClient.AssertExpectations(t)

		expectBody := receiptverify.PostReceiptverifyBody{
			AllowExpired:   req.AllowExpired,
			AppServiceId:   req.AppServiceId,
			PlatformUserId: req.PlatformUserId,
			Receipt:        req.Receipt,
		}

		expectHeaders := receiptverify.PostReceiptverifyHeaderParams{
			XApiKey: apikey,
		}

		mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, expectBody, expectHeaders)
		mockReceiptVerifyClient.AssertNotCalled(t, "PostReceiptverifyValidateAppstoreTransaction", mock.Anything, mock.Anything, mock.Anything)
	})
}

func TestBuildSetPurchase(t *testing.T) {
	baseTime := time.Now().UTC()
	tests := []struct {
		name             string
		req              *models.V1PostPurchaseRequest
		uid              string
		appId            string
		pid              string
		purchaseId       string
		transactionId    string
		nextCheckDate    *time.Time
		validReceipt     bool
		validTransaction bool
		receipt          *receiptverifyModels.V1PostReceiptverifyResponse
		expected         purchase.V1SetPurchase
	}{
		{
			name: "purchase without free trial receipt",
			req: &models.V1PostPurchaseRequest{
				AllowExpired:          utils.ToPtr(true),
				AppServiceId:          "appServiceId",
				IsAppstoreTransaction: utils.ToPtr(true),
				Label:                 models.V1StoreLabelAmazonstore.String(),
				PlatformUserId:        utils.ToPtr("platformUserId"),
				Receipt:               "receipt",
			},
			uid:              "uid",
			appId:            "appId",
			pid:              "Pid",
			purchaseId:       "purchaseId",
			transactionId:    "transactionId",
			nextCheckDate:    &baseTime,
			validReceipt:     true,
			validTransaction: true,
			receipt:          nil,
			expected: purchase.V1SetPurchase{
				Uid:                   "uid",
				AppId:                 "appId",
				Pid:                   "Pid",
				Store:                 models.V1StoreLabelAmazonstore,
				PurchaseId:            "purchaseId",
				AppServiceID:          "appServiceId",
				Receipt:               "receipt",
				TransactionId:         "transactionId",
				PlatformUserId:        utils.ToPtr("platformUserId"),
				NextCheckDate:         &baseTime,
				ValidReceipt:          true,
				ValidTransaction:      true,
				FreeTrialDays:         0,
				IsAppstoreTransaction: true,
			},
		},
		{
			name: "purchase with free trial receipt",
			req: &models.V1PostPurchaseRequest{
				AppServiceId:          "appServiceId",
				IsAppstoreTransaction: utils.ToPtr(false),
				Label:                 models.V1StoreLabelAppstore.String(),
				PlatformUserId:        utils.ToPtr("platformUserId"),
				Receipt:               "receipt",
			},
			uid:              "uid2",
			appId:            "appId",
			pid:              "Pid2",
			purchaseId:       "purchaseId2",
			transactionId:    "transactionId2",
			nextCheckDate:    &baseTime,
			validReceipt:     true,
			validTransaction: true,
			receipt: &receiptverifyModels.V1PostReceiptverifyResponse{
				FreeTrial: utils.ToPtr(true),
				StartDate: baseTime,
				EndDate:   utils.ToPtr(baseTime.AddDate(0, 0, 7)),
			},
			expected: purchase.V1SetPurchase{
				Uid:                   "uid2",
				AppId:                 "appId",
				Pid:                   "Pid2",
				Store:                 models.V1StoreLabelAppstore,
				PurchaseId:            "purchaseId2",
				AppServiceID:          "appServiceId",
				Receipt:               "receipt",
				TransactionId:         "transactionId2",
				PlatformUserId:        utils.ToPtr("platformUserId"),
				NextCheckDate:         &baseTime,
				ValidReceipt:          true,
				ValidTransaction:      true,
				FreeTrialDays:         7,
				IsAppstoreTransaction: false,
			},
		},
		{
			name: "roku purchase",
			req: &models.V1PostPurchaseRequest{
				AllowExpired:          utils.ToPtr(true),
				AppServiceId:          "appServiceId",
				IsAppstoreTransaction: utils.ToPtr(true),
				Label:                 models.V1StoreLabelAmazonstore.String(),
				PlatformUserId:        utils.ToPtr("platformUserId"),
				Receipt:               "receipt",
				RokuPucId:             utils.ToPtr("rokuPucId"),
			},
			uid:              "uid",
			appId:            "appId",
			pid:              "Pid",
			purchaseId:       "purchaseId",
			transactionId:    "transactionId",
			nextCheckDate:    &baseTime,
			validReceipt:     true,
			validTransaction: true,
			receipt:          nil,
			expected: purchase.V1SetPurchase{
				Uid:                   "uid",
				AppId:                 "appId",
				Pid:                   "Pid",
				Store:                 models.V1StoreLabelAmazonstore,
				PurchaseId:            "purchaseId",
				AppServiceID:          "appServiceId",
				Receipt:               "receipt",
				TransactionId:         "transactionId",
				PlatformUserId:        utils.ToPtr("platformUserId"),
				NextCheckDate:         &baseTime,
				ValidReceipt:          true,
				ValidTransaction:      true,
				FreeTrialDays:         0,
				IsAppstoreTransaction: true,
				RokuPucId:             utils.ToPtr("rokuPucId"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := buildSetPurchase(context.Background(), tt.req, tt.uid, tt.appId, tt.pid, tt.purchaseId, tt.transactionId, tt.nextCheckDate, tt.validReceipt, tt.validTransaction, tt.receipt)
			assert.Equal(t, tt.expected, got)
		})
	}
}

func TestGetNextCheckDateForSamsung(t *testing.T) {
	t.Run("samsung date is zero", func(t *testing.T) {
		now := time.Now()
		rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
			Store:           "tizen",
			NextPaymentDate: &now,
		}

		inputTime := time.Time{}

		got, err := getNextCheckDateForSamsung(rvr, &inputTime)

		assert.NoError(t, err)
		assert.Equal(t, &now, got)
	})
	// this unit tests just checks for no error, but what should we really return?
	t.Run("next payment date equal to samsung purchase record", func(t *testing.T) {
		now := time.Now()
		rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
			Store:           "tizen",
			NextPaymentDate: &now,
		}

		got, err := getNextCheckDateForSamsung(rvr, &now)

		assert.NoError(t, err)
		assert.Equal(t, &now, got)
	})

	t.Run("samsung purchase date earlier than reciept", func(t *testing.T) {
		now := time.Now()
		tomorrow := now.AddDate(0, 0, +10)
		rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
			Store:           "tizen",
			NextPaymentDate: &tomorrow,
		}

		got, err := getNextCheckDateForSamsung(rvr, &now)

		assert.NoError(t, err)
		assert.Equal(t, &now, got)
	})
	t.Run("reciept verify date earlier than samsung", func(t *testing.T) {
		now := time.Now()
		tomorrow := now.AddDate(0, 0, +10)
		rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
			Store:           "tizen",
			NextPaymentDate: &now,
		}

		got, err := getNextCheckDateForSamsung(rvr, &tomorrow)

		assert.NoError(t, err)
		assert.Equal(t, &now, got)
	})
	t.Run("store is not tizen", func(t *testing.T) {
		today := time.Now()
		tomorrow := today.AddDate(0, 0, +10)
		rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
			Store:           "not-tizen",
			NextPaymentDate: &today,
		}

		got, err := getNextCheckDateForSamsung(rvr, &tomorrow)

		assert.NoError(t, err)
		assert.Nil(t, got)
	})

	t.Run("next payment date is zero", func(t *testing.T) {
		rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
			Store:           "tizen",
			NextPaymentDate: &time.Time{},
		}
		today := time.Now()
		tomorrow := today.AddDate(0, 0, +10)

		got, err := getNextCheckDateForSamsung(rvr, &tomorrow)

		assert.Error(t, err)
		assert.Nil(t, got)
	})

	t.Run("next payment date is nil", func(t *testing.T) {
		rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
			Store:           "tizen",
			NextPaymentDate: nil,
		}
		got, err := getNextCheckDateForSamsung(rvr, nil)

		assert.Error(t, err)
		assert.Nil(t, got)
	})

	t.Run("success", func(t *testing.T) {
		now := time.Now()
		rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
			Store:           "tizen",
			NextPaymentDate: &now,
		}
		tomorrow := now.AddDate(0, 0, +10)

		got, err := getNextCheckDateForSamsung(rvr, &tomorrow)

		assert.NoError(t, err)
		assert.Equal(t, &now, got)
	})
}

func TestBuildEntitlementChargedFromReceiptVerify(t *testing.T) {
	now := time.Now()
	testCases := []struct {
		name   string
		input  *receiptverifyModels.V1Charged
		output *entitlement.V1EntitlementCharged
	}{
		{
			name:  "nil input",
			input: nil,
			output: &entitlement.V1EntitlementCharged{
				Amount:   0,
				Currency: "USD",
			},
		},
		{
			name: "valid input",
			input: &receiptverifyModels.V1Charged{
				Currency: "USD",
				Date:     now,
			},
			output: &entitlement.V1EntitlementCharged{
				Currency: "USD",
				Date:     now,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := buildEntitlementChargedFromReceiptVerify(tc.input, 0.0)
			assert.Equal(t, tc.output.Amount, result.Amount)
			assert.Equal(t, tc.output.Currency, result.Currency)
		})
	}
}

func TestV1PostPurchase(t *testing.T) {
	stat, _ := stats.New("V1PostPurchase", "v1", stats.WithDevMode())
	l, _ := logger.New(
		logger.WithLogLevel("info"),
		logger.WithoutTimestamp(),
	)

	cases := []struct {
		name               string
		deps               *V1PurchaseDependency
		expectedStatusCode int
		want               interface{}
		buildTest          func() (*Clients, *http.Request, func())
	}{
		{
			name:               "bad request: empty receipt",
			expectedStatusCode: 400,
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
			},
			buildTest: func() (*Clients, *http.Request, func()) {
				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(&models.V1PostPurchaseRequest{Label: "tizen", AppServiceId: "service"})
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				return &Clients{}, req, nil
			},
			want: response.MakeFromError(400, []error{fmt.Errorf("validation error: field: receipt:  required: receipt is required to not be empty")}),
		},
		{
			name:               "bad request: missing authorization header",
			expectedStatusCode: 400,
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
			},
			buildTest: func() (*Clients, *http.Request, func()) {
				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(&models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"})
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				return &Clients{}, req, nil
			},
			want: response.MakeFromError(400, []error{utils.ErrFieldRequired(fmt.Sprintf("header.%s OR header.%s for claims", jwtdecoder.AccessTokenHeader, jwtdecoder.AuthorizationHeader)).Err()}),
		},
		{
			name:               "bad request: missing apikey",
			expectedStatusCode: 400,
			buildTest: func() (*Clients, *http.Request, func()) {
				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(&models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"})
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				return &Clients{}, req, nil
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
			},
			want: response.MakeFromError(400, []error{utils.ErrFieldRequired("header." + request.APIKeyHeader).Err()}),
		},
		{
			name:               "GetUserDetails returns an error",
			expectedStatusCode: 500,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(nil, 500, errors.New("something went wrong"))

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				return &Clients{
					accountV2: mockAccountV2Client,
				}, req, nil
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
			},
			want: response.MakeFromError(500, []error{errors.New("something went wrong")}),
		},
		{
			name:               "user doesn't have email",
			expectedStatusCode: 500,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				return &Clients{
					accountV2: mockAccountV2Client,
				}, req, nil
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
			},
			want: response.MakeFromError(500, []error{errors.New("user has no email associated")}),
		},
		{
			name:               "failed to verify receipt",
			expectedStatusCode: 500,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return((*receiptverifyModels.V1PostReceiptverifyResponse)(nil), 500, errors.New("something went wrong"))

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					mockReceiptVerifyClient.AssertExpectations(t)

					expectBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					expectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}

					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, expectBody, expectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
			},
			want: response.MakeFromError(500, []error{errors.New("something went wrong")}),
		},

		{
			name:               "failed to verify receipt, failed to save purchase",
			expectedStatusCode: 500,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return((*receiptverifyModels.V1PostReceiptverifyResponse)(nil), 400, errors.New("something went wrong"))

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					mockReceiptVerifyClient.AssertExpectations(t)

					expectBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					expectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}

					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, expectBody, expectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, errors.New("something went wrong saving purchase")
					},
				},
			},
			want: response.MakeFromError(500, []error{errors.New("something went wrong saving purchase")}),
		},
		{
			name:               "failed to verify receipt, failed to save purchase",
			expectedStatusCode: 400,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return((*receiptverifyModels.V1PostReceiptverifyResponse)(nil), 400, errors.New("something went wrong"))

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					mockReceiptVerifyClient.AssertExpectations(t)

					expectBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					expectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}

					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, expectBody, expectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
			},
			want: response.MakeFromError(400, []error{errors.New("something went wrong")}),
		},
		{
			name:               "failed to compute next check date",
			expectedStatusCode: 500,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(&receiptverifyModels.V1PostReceiptverifyResponse{
					Store:     "tizen",
					StartDate: time.Now(),
					EndDate:   utils.ToPtr(time.Now().AddDate(1, 0, 0)),
				}, 200, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					mockReceiptVerifyClient.AssertExpectations(t)

					expectBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					expectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}

					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, expectBody, expectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{Receipt: "receipt", Id: "user-id", NextCheckDate: time.Now()}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Receipt:     "receipt",
							Transaction: transactionId,
							Pid:         "user-uid",
							Uid:         "user-uid",
						}, nil
					},
				},
			},
			want: response.MakeFromError(500, []error{errors.New("next payment date is empty")}),
		},
		{
			name:               "error: find transaction",
			expectedStatusCode: 500,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(&receiptverifyModels.V1PostReceiptverifyResponse{
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					StartDate:       time.Now(),
					EndDate:         utils.ToPtr(time.Now().AddDate(1, 0, 0)),
				}, 200, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					mockReceiptVerifyClient.AssertExpectations(t)

					expectBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					expectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}

					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, expectBody, expectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, errors.New("something went wrong")
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(500, []error{errors.New("something went wrong")}),
		},
		{
			name:               "error: purchase already exists",
			expectedStatusCode: 400,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(&receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					StartDate:       time.Now(),
					EndDate:         utils.ToPtr(time.Now().AddDate(1, 0, 0)),
				}, 200, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user-uid"})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					mockReceiptVerifyClient.AssertExpectations(t)

					expectBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					expectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}

					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, expectBody, expectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{
							Receipt: "receipt",
							Id:      "user-uid",
						}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Receipt:     "receipt",
							Transaction: transactionId,
						}, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(400, []error{errors.New("receipt with transaction id transactionId belongs to a different user")}),
		},
		{
			name:               "error: failed to renew entitlement",
			expectedStatusCode: 500,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					StartDate:       time.Now(),
					EndDate:         utils.ToPtr(time.Now().AddDate(1, 0, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount: 64.99,
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PutEntitlementRenew", mock.Anything, mock.Anything).Return(nil, errors.New("entitlements - something went wrong"))

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey, Brand: "foxnation"})
				rh.SetJWTToken("token")

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
					entitlements:  mockEntitlementClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{
							Receipt: "receipt-old",
							// Id:      "user-uid",
						}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Receipt:     "receipt-old",
							Transaction: transactionId,
							Pid:         "user-uid",
							Uid:         "user-uid",
						}, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(500, []error{errors.New("entitlements - something went wrong")}),
		},

		{
			name:               "success: renew entitlement",
			expectedStatusCode: 200,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					StartDate:       time.Now(),
					EndDate:         utils.ToPtr(time.Now().AddDate(1, 0, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   64.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PutEntitlementRenew", mock.Anything, mock.Anything).Return(&entitlement.V1PutEntitlemenRenewResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPublisherClient.Mock.On("PublishPnRNotification", mock.Anything, mock.Anything).Return(nil)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken("token")

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					publisherClient: mockPublisherClient,
					plans:           mockPlansClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{
							Receipt: "receipt-old",
							Id:      "user-uid",
						}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Receipt:     "receipt-old",
							Uid:         "user-uid",
							Transaction: transactionId,
						}, nil
					},
				},

				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: &entitlement.V1EntitlementObject{
				PurchaseId: "purchaseId",
			},
		},

		{
			name:               "success: renew entitlement with FlexibleOfferPrice",
			expectedStatusCode: 200,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:      "transactionId",
					Store:              "tizen",
					NextPaymentDate:    utils.ToPtr(time.Now()),
					FlexibleOfferPrice: "100.99",
					StartDate:          time.Now(),
					EndDate:            utils.ToPtr(time.Now().AddDate(1, 0, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PutEntitlementRenew", mock.Anything, mock.Anything).Return(&entitlement.V1PutEntitlemenRenewResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPublisherClient.Mock.On("PublishPnRNotification", mock.Anything, mock.Anything).Return(nil)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken("token")

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					publisherClient: mockPublisherClient,
					plans:           mockPlansClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{
							Receipt: "receipt-old",
							Id:      "user-uid",
						}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Receipt:     "receipt-old",
							Uid:         "user-uid",
							Transaction: transactionId,
						}, nil
					},
				},

				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: &entitlement.V1EntitlementObject{
				PurchaseId: "purchaseId",
			},
		},

		{
			name:               "error: failed publishing the purchase confirmation event in renew entitlement",
			expectedStatusCode: 424,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					StartDate:       time.Now(),
					EndDate:         utils.ToPtr(time.Now().AddDate(1, 0, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PutEntitlementRenew", mock.Anything, mock.Anything).Return(&entitlement.V1PutEntitlemenRenewResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(assert.AnError)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken("token")

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					publisherClient: mockPublisherClient,
					plans:           mockPlansClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{
							Receipt: "receipt-old",
							Id:      "user-uid",
						}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Receipt:     "receipt-old",
							Uid:         "user-uid",
							Transaction: transactionId,
						}, nil
					},
				},

				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(424, []error{errors.New("assert.AnError general error for testing")}),
		},

		{
			name:               "error: no token in header",
			expectedStatusCode: 400,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					StartDate:       time.Now(),
					EndDate:         utils.ToPtr(time.Now().AddDate(1, 0, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						token, ok := ctx.Value("token").(string)
						if !ok || token == "" {
							return nil, utils.ErrFieldRequired("token")
						}
						return &models.V1SamsungPurchase{
							NextCheckDate: time.Now(),
							Receipt:       "receipt",
							Id:            "user-uid",
						}, nil
					},

					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, utils.ErrEntryNotFound
						// return nil, nil
					},
				},

				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(400, []error{utils.ErrFieldRequired(fmt.Sprintf("header.%s OR header.%s", jwtdecoder.AccessTokenHeader, jwtdecoder.AuthorizationHeader)).Err()}),
		},

		{
			name:               "error: failed to create entitlement - no purchase saved",
			expectedStatusCode: 424,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					StartDate:       time.Now(),
					EndDate:         utils.ToPtr(time.Now().AddDate(1, 0, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("entitlements - something went wrong"))

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				token := "token"

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
					entitlements:  mockEntitlementClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{
							Receipt:       "receipt",
							Id:            "user-uid",
							NextCheckDate: time.Now(),
						}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(424, []error{errors.New("entitlements - something went wrong")}),
		},

		{
			name:               "error: failed to create entitlement - failed to set purchase",
			expectedStatusCode: 424,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					StartDate:       time.Now(),
					EndDate:         utils.ToPtr(time.Now().AddDate(1, 0, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("entitlements - something went wrong"))

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				token := "token"

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
					entitlements:  mockEntitlementClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, errors.New("purchasesvc - something went wrong")
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{
							Receipt: "receipt",
							Id:      "user-uid",
						}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(424, []error{errors.New("purchasesvc - something went wrong")}),
		},

		{
			name:               "error: failed to create entitlement - success setting purchase",
			expectedStatusCode: 424,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("entitlements - something went wrong"))

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				token := "token"

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
					entitlements:  mockEntitlementClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},

					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(424, []error{errors.New("entitlements - something went wrong")}),
		},

		{
			name:               "error: failed to set purchase",
			expectedStatusCode: 500,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1PostEntitlemenResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				token := "token"

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:     mockAccountV2Client,
					receiptVerify: mockReceiptVerifyClient,
					entitlements:  mockEntitlementClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, errors.New("purchasesvc - something went wrong")
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return &models.V1SamsungPurchase{
							Receipt: "receipt",
							Id:      "user-uid",
						}, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(500, []error{errors.New("purchasesvc - something went wrong")}),
		},

		{
			name:               "error: failed publishing purchase confirmation notification",
			expectedStatusCode: 424,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					EndDate:         utils.ToPtr(time.Now().AddDate(0, 1, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1PostEntitlemenResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(assert.AnError)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				token := "token"

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					plans:           mockPlansClient,
					publisherClient: mockPublisherClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return nil, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: response.MakeFromError(424, []error{errors.New("assert.AnError general error for testing")}),
		},

		{
			name:               "success",
			expectedStatusCode: 200,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					EndDate:         utils.ToPtr(time.Now().AddDate(0, 1, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1PostEntitlemenResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPublisherClient.Mock.On("PublishPnRNotification", mock.Anything, mock.Anything).Return(nil)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				token := "token"

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()
				uid := "user-uid"
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					plans:           mockPlansClient,
					publisherClient: mockPublisherClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				cfg: &config.Config{
					UseEvergent: "false",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return nil, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: &entitlement.V1EntitlementObject{
				PurchaseId: "purchaseId",
			},
		},

		{
			name:               "success with evergent",
			expectedStatusCode: 200,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "tizen", AppServiceId: "service"}

				apikey := "apikey"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "tizen",
					NextPaymentDate: utils.ToPtr(time.Now()),
					EndDate:         utils.ToPtr(time.Now().AddDate(0, 1, 0)),
					Charged: &receiptverifyModels.V1Charged{
						Amount:   100.99,
						Currency: "USD",
						Date:     time.Now(),
					},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1PostEntitlemenResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPublisherClient.Mock.On("PublishPnRNotification", mock.Anything, mock.Anything).Return(nil)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				mockEvergentProxyClient := newEvergentProxyMock()
				mockEvergentProxyClient.Mock.On("SearchUser", mock.Anything, mock.Anything).Return(
					evergentproxy.V1SearchUserOutput{
						Found: true,
					},
					nil,
				)
				mockEvergentProxyClient.Mock.On("AddSubscription", mock.Anything, mock.Anything).Return(
					evergentproxy.V1AddSubscriptionOutput{},
					nil,
				)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				token := "token"

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)
				uid := "user-uid"

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					plans:           mockPlansClient,
					publisherClient: mockPublisherClient,
					evergentproxy:   mockEvergentProxyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				cfg: &config.Config{
					UseEvergent: "true",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
					AllowBypassStoresList: "tizen",
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return nil, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: &entitlement.V1EntitlementObject{
				PurchaseId: "purchaseId",
			},
		},
		{
			name:               "success with amazon and evergent and missing price from receiptverify",
			expectedStatusCode: 200,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "amazonstore", AppServiceId: "service", PriceCharged: x.Ptr(100.99), PlatformUserId: utils.ToPtr("platformUserID")}

				apikey := "apikey"
				uid := "user-uid"
				token := "token"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "amazonstore",
					NextPaymentDate: utils.ToPtr(time.Now()),
					EndDate:         utils.ToPtr(time.Now().AddDate(0, 1, 0)),
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1PostEntitlemenResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPublisherClient.Mock.On("PublishPnRNotification", mock.Anything, mock.Anything).Return(nil)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				mockEvergentProxyClient := newEvergentProxyMock()
				mockEvergentProxyClient.Mock.On("SearchUser", mock.Anything, mock.Anything).Return(
					evergentproxy.V1SearchUserOutput{
						Found: true,
					},
					nil,
				)
				expectedPriceCharged := utils.ToPtr("100.99")
				addSubscriptionInputExpected := evergentproxy.V1AddSubscriptionInput{
					AppServiceId:   reqBody.AppServiceId,
					Label:          reqBody.Label,
					PurchaseID:     "purchaseId",
					UID:            uid,
					AccessToken:    token,
					PriceCharged:   *expectedPriceCharged,
					PlatformUserId: "platformUserID",
					Receipt:        reqBody.Receipt,
				}
				mockEvergentProxyClient.Mock.On("AddSubscription", mock.Anything, addSubscriptionInputExpected).Return(
					evergentproxy.V1AddSubscriptionOutput{},
					nil,
				)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey, Brand: "foxnation"})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)

					postEntitlementRenewExpectedHeader := entitlement.PostEntitlementHeaderParams{
						XApiKey:      apikey,
						XAccessToken: token,
					}

					mockEntitlementClient.AssertExpectations(t)
					mockEntitlementClient.AssertCalled(t, "PostEntitlement", mock.Anything, mock.Anything, postEntitlementRenewExpectedHeader)

					mockEvergentProxyClient.AssertExpectations(t)
					mockEvergentProxyClient.AssertCalled(t, "SearchUser", mock.Anything, mock.Anything)
					mockEvergentProxyClient.AssertCalled(t, "AddSubscription", mock.Anything, addSubscriptionInputExpected)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					plans:           mockPlansClient,
					publisherClient: mockPublisherClient,
					evergentproxy:   mockEvergentProxyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				cfg: &config.Config{
					UseEvergent: "true",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
					AllowBypassStoresList: "amazonstore",
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return nil, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: &entitlement.V1EntitlementObject{
				PurchaseId: "purchaseId",
			},
		},
		{
			name:               "success - missing receiptverify charged and input charged object",
			expectedStatusCode: 200,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "amazonstore", AppServiceId: "service", PlatformUserId: utils.ToPtr("platformUserID")}

				apikey := "apikey"
				uid := "user-uid"
				token := "token"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "amazonstore",
					NextPaymentDate: utils.ToPtr(time.Now()),
					EndDate:         utils.ToPtr(time.Now().AddDate(0, 1, 0)),
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1PostEntitlemenResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPublisherClient.Mock.On("PublishPnRNotification", mock.Anything, mock.Anything).Return(nil)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				mockEvergentProxyClient := newEvergentProxyMock()
				mockEvergentProxyClient.Mock.On("SearchUser", mock.Anything, mock.Anything).Return(
					evergentproxy.V1SearchUserOutput{
						Found: true,
					},
					nil,
				)
				addSubscriptionInputExpected := evergentproxy.V1AddSubscriptionInput{
					AppServiceId:   reqBody.AppServiceId,
					Label:          reqBody.Label,
					PurchaseID:     "purchaseId",
					UID:            uid,
					AccessToken:    token,
					PlatformUserId: "platformUserID",
					Receipt:        reqBody.Receipt,
					PriceCharged:   "0.00",
				}
				mockEvergentProxyClient.Mock.On("AddSubscription", mock.Anything, addSubscriptionInputExpected).Return(
					evergentproxy.V1AddSubscriptionOutput{},
					nil,
				)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()
				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey, Brand: "foxnation"})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)

					postEntitlementRenewExpectedHeader := entitlement.PostEntitlementHeaderParams{
						XApiKey:      apikey,
						XAccessToken: token,
					}
					mockEntitlementClient.AssertExpectations(t)
					mockEntitlementClient.AssertCalled(t, "PostEntitlement", mock.Anything, mock.Anything, postEntitlementRenewExpectedHeader)

					mockEvergentProxyClient.AssertExpectations(t)
					mockEvergentProxyClient.AssertCalled(t, "SearchUser", mock.Anything, mock.Anything)
					mockEvergentProxyClient.AssertCalled(t, "AddSubscription", mock.Anything, addSubscriptionInputExpected)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					plans:           mockPlansClient,
					publisherClient: mockPublisherClient,
					evergentproxy:   mockEvergentProxyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				cfg: &config.Config{
					UseEvergent: "true",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
					AllowBypassStoresList: "amazonstore",
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return nil, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: &entitlement.V1EntitlementObject{
				PurchaseId: "purchaseId",
			},
		},
		{
			name:               "success - addSubscription was sent a receipt",
			expectedStatusCode: 200,
			buildTest: func() (*Clients, *http.Request, func()) {
				reqBody := &models.V1PostPurchaseRequest{Receipt: "receipt", Label: "amazonstore", AppServiceId: "service", PlatformUserId: utils.ToPtr("platformUserID")}

				apikey := "apikey"
				uid := "user-uid"
				token := "token"

				mockReceiptVerifyClient := receiptverify.NewClientMock()
				rvr := &receiptverifyModels.V1PostReceiptverifyResponse{
					TransactionId:   "transactionId",
					Store:           "amazonstore",
					NextPaymentDate: utils.ToPtr(time.Now()),
					EndDate:         utils.ToPtr(time.Now().AddDate(0, 1, 0)),
					Charged:         &receiptverifyModels.V1Charged{Amount: 100.0, Currency: "USD", Date: time.Now()},
				}
				mockReceiptVerifyClient.Mock.On("PostReceiptverify", mock.Anything, mock.Anything, mock.Anything).Return(rvr, 200, nil)

				mockEntitlementClient := newEntitlementMock()
				mockEntitlementClient.Mock.On("PostEntitlement", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1PostEntitlemenResponse{
					Entitlement: &entitlement.V1EntitlementObject{
						PurchaseId: "purchaseId",
					},
				}, nil)

				userRecord := &accountV2Models.UserRecord{
					FirstName: "name",
					Email:     "name@fox",
					HasEmail:  true,
				}
				mockAccountV2Client := accountV2.NewClientMock()
				mockAccountV2Client.Mock.On("GetUserDetails", mock.Anything, mock.Anything, mock.Anything).Return(userRecord, 200, nil)

				mockPublisherClient := &mockNotificationPublisherClient{}
				mockPublisherClient.Mock.On("PublishBrazeNotification", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPublisherClient.Mock.On("PublishPnRNotification", mock.Anything, mock.Anything).Return(nil)

				mockPlansClient := &plansClientMock{}
				mockPlansClient.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plans.Plan{
					Tier:         200,
					AppServiceID: "service",
					Period: &plans.Period{
						PeriodUnit: "monthly",
					},
				}, nil)

				mockEvergentProxyClient := newEvergentProxyMock()
				mockEvergentProxyClient.Mock.On("SearchUser", mock.Anything, mock.Anything).Return(
					evergentproxy.V1SearchUserOutput{
						Found: true,
					},
					nil,
				)
				addSubscriptionInputExpected := evergentproxy.V1AddSubscriptionInput{
					AppServiceId:   reqBody.AppServiceId,
					Label:          reqBody.Label,
					PurchaseID:     "purchaseId",
					UID:            uid,
					AccessToken:    token,
					PlatformUserId: "platformUserID",
					Receipt:        reqBody.Receipt,
					PriceCharged:   "100.00",
				}
				mockEvergentProxyClient.Mock.On("AddSubscription", mock.Anything, addSubscriptionInputExpected).Return(
					evergentproxy.V1AddSubscriptionOutput{},
					nil,
				)

				body := new(bytes.Buffer)
				err := json.NewEncoder(body).Encode(reqBody)
				if err != nil {
					t.Fatal(err)
				}
				req := httptest.NewRequest("POST", "/purchase", body)

				req.Header.Set(jwtdecoder.AccessTokenHeader, token)

				rh := request.New()

				rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
				rh.SetAPIKey(apikeys.APIKey{APIKey: apikey, Brand: "foxnation"})
				rh.SetJWTToken(token)

				ctx := request.NewContext(req.Context(), rh)
				req = req.WithContext(ctx)

				postRun := func() {
					postReceiptverifyExpectedBody := receiptverify.PostReceiptverifyBody{
						AllowExpired:   reqBody.AllowExpired,
						AppServiceId:   reqBody.AppServiceId,
						PlatformUserId: reqBody.PlatformUserId,
						Receipt:        reqBody.Receipt,
					}
					postReceiptverifyExpectHeaders := receiptverify.PostReceiptverifyHeaderParams{
						XApiKey: apikey,
					}
					mockReceiptVerifyClient.AssertExpectations(t)
					mockReceiptVerifyClient.AssertCalled(t, "PostReceiptverify", mock.Anything, postReceiptverifyExpectedBody, postReceiptverifyExpectHeaders)

					mockEvergentProxyClient.AssertExpectations(t)
					mockEvergentProxyClient.AssertCalled(t, "SearchUser", mock.Anything, mock.Anything)
					mockEvergentProxyClient.AssertCalled(t, "AddSubscription", mock.Anything, addSubscriptionInputExpected)
				}

				return &Clients{
					accountV2:       mockAccountV2Client,
					receiptVerify:   mockReceiptVerifyClient,
					entitlements:    mockEntitlementClient,
					plans:           mockPlansClient,
					publisherClient: mockPublisherClient,
					evergentproxy:   mockEvergentProxyClient,
				}, req, postRun
			},
			deps: &V1PurchaseDependency{
				stat: stat,
				log:  l,
				utils: Utils{
					purchaseIDCreator: func() string {
						return "purchaseId"
					},
				},
				cfg: &config.Config{
					UseEvergent: "true",
					AccountV2URL: map[string]string{
						"dev1": "http://mock-accountv2-url-for-dev1",
					},
					AllowBypassStoresList: "amazonstore",
				},
				purchaseSvc: &mockPurchaseSvc{
					set: func(ctx context.Context, purchase purchase.V1SetPurchase) (*models.V1Purchase, error) {
						return nil, nil
					},
					getSamsungPurchaseByReceipt: func(ctx context.Context, id string) (*models.V1SamsungPurchase, error) {
						return nil, nil
					},
					findTransaction: func(ctx context.Context, store models.V1StoreLabel, transactionId string, validTransaction bool) (*models.V1Purchase, error) {
						return nil, nil
					},
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{}, S: map[string]string{}},
			},
			want: &entitlement.V1EntitlementObject{
				PurchaseId: "purchaseId",
			},
		},
	}

	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			rw := httptest.NewRecorder()

			clients, req, postRun := tt.buildTest()
			// mock assertions are done in postRun
			defer func() {
				if postRun != nil {
					postRun()
				}
			}()
			tt.deps.clients = *clients
			tt.deps.brazeDeps = braze.MakeDeps(stat, nil, nil, nil)

			h := newHandler(tt.deps)

			router := chi.NewRouter()
			router.
				With(PostPurchaseInputValidator(route.Route{})).
				Post("/purchase", h.V1PostPurchase)
			router.ServeHTTP(rw, req)

			assert.Equal(t, tt.expectedStatusCode, rw.Code)

			expectedBody, err := json.Marshal(tt.want)
			assert.NoError(t, err)
			assert.Equal(t, string(expectedBody)+"\n", rw.Body.String())
		})
	}
}

func TestV1PostPurchaseNoInputValidator(t *testing.T) {
	rw := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/purchase", nil)

	req.Header.Set(jwtdecoder.AccessTokenHeader, "token")
	rh := request.New()
	uid := "user-uid"
	rh.SetJWTClaims(&jwtdecoder.Claims{Uid: uid})
	rh.SetAPIKey(apikeys.APIKey{APIKey: "apikey"})
	rh.SetJWTToken("token")
	ctx := request.NewContext(req.Context(), rh)
	req = req.WithContext(ctx)

	stat, _ := stats.New("TestV1PostPurchase", "v1", stats.WithDevMode())

	l, _ := logger.New(
		logger.WithLogLevel("info"),
		logger.WithoutTimestamp(),
	)

	deps := &V1PurchaseDependency{
		stat: stat,
		log:  l,
	}

	h := newHandler(deps)

	router := chi.NewRouter()
	router.Post("/purchase", h.V1PostPurchase)
	router.ServeHTTP(rw, req)

	assert.Equal(t, 400, rw.Code)

	want := response.MakeFromError(400, []error{errors.New("request input not found")})
	expectedBody, err := json.Marshal(want)
	assert.NoError(t, err)
	assert.Equal(t, string(expectedBody)+"\n", rw.Body.String())
}

func TestIsNotSubscribedToEvergentError(t *testing.T) {
	cases := []struct {
		name     string
		errorRsp error
		want     bool
	}{
		{
			name:     "not subscribed",
			errorRsp: errors.New("network error"),
			want:     true,
		},
		{
			name:     "subscribed",
			errorRsp: fmt.Errorf("error: %s", AlreadySubscribedEvergentErrorCode),
			want:     false,
		},
	}

	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.want, IsNotSubscribedToEvergentError(tt.errorRsp))
		})
	}
}

func TestGetPlaystorePurchaseTokenFromEncodedReceipt(t *testing.T) {
	// Google receipts contains different values like: orderId, packageName, productId, purchaseToken, among others.
	// For the sake of simplicity, receipts on these test cases will only contain the 'purchaseToken' field
	cases := []struct {
		name    string
		receipt func() string
		want    string
	}{
		{
			name: "failure - receipt is not base64 encoded",
			receipt: func() string {
				return `{
	"purchaseToken": ""
}`
			},
			want: "",
		},
		{
			name: "failure - invalid JSON",
			receipt: func() string {
				receipt := `{
			"purchaseToken"
		}`
				return base64.StdEncoding.EncodeToString([]byte(receipt))
			},
			want: "",
		},
		{
			name: "failure - purchaseToken is empty",
			receipt: func() string {
				receipt := `{
			"purchaseToken": ""
		}`
				return base64.StdEncoding.EncodeToString([]byte(receipt))
			},
			want: "",
		},
		{
			name: "failure - purchaseToken is not a string",
			receipt: func() string {
				receipt := `{
			"purchaseToken": 1234
		}`
				return base64.StdEncoding.EncodeToString([]byte(receipt))
			},
			want: "",
		},
		{
			name: "success",
			receipt: func() string {
				receipt := `{
			"purchaseToken": "test-purchaseToken"
		}`
				return base64.StdEncoding.EncodeToString([]byte(receipt))
			},
			want: "test-purchaseToken",
		},
	}

	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			purchaseToken := getPlaystorePurchaseTokenFromEncodedReceipt(ctx, tt.receipt())
			assert.Equal(t, tt.want, purchaseToken)
		})
	}
}

func TestCalculateFreeTrialDays(t *testing.T) {
	baseTime := time.Now().UTC()

	tests := []struct {
		name     string
		receipt  *receiptverifyModels.V1PostReceiptverifyResponse
		store    models.V1StoreLabel
		expected int64
	}{
		{
			name: "free trial is nil",
			receipt: &receiptverifyModels.V1PostReceiptverifyResponse{
				FreeTrial: nil,
				StartDate: baseTime,
				EndDate:   utils.ToPtr(baseTime.AddDate(0, 0, 7)),
			},
			store:    models.V1StoreLabelAppstore,
			expected: 0,
		},
		{
			name: "free trial is false",
			receipt: &receiptverifyModels.V1PostReceiptverifyResponse{
				FreeTrial: utils.ToPtr(false),
				StartDate: baseTime,
				EndDate:   utils.ToPtr(baseTime.AddDate(0, 0, 7)),
			},
			store:    models.V1StoreLabelAppstore,
			expected: 0,
		},
		{
			name: "tizen store - 7 days trial",
			receipt: &receiptverifyModels.V1PostReceiptverifyResponse{
				FreeTrial:       utils.ToPtr(true),
				StartDate:       baseTime,
				NextPaymentDate: utils.ToPtr(baseTime.AddDate(0, 0, 7)),
			},
			store:    models.V1StoreLabelTizen,
			expected: 7,
		},
		{
			name: "appstore - 7 days trial",
			receipt: &receiptverifyModels.V1PostReceiptverifyResponse{
				FreeTrial: utils.ToPtr(true),
				StartDate: baseTime,
				EndDate:   utils.ToPtr(baseTime.AddDate(0, 0, 7)),
			},
			store:    models.V1StoreLabelAppstore,
			expected: 7,
		},
		{
			name: "playstore - 14 days trial",
			receipt: &receiptverifyModels.V1PostReceiptverifyResponse{
				FreeTrial: utils.ToPtr(true),
				StartDate: baseTime,
				EndDate:   utils.ToPtr(baseTime.AddDate(0, 0, 14)),
			},
			store:    models.V1StoreLabelPlaystore,
			expected: 14,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := calculateFreeTrialDays(tt.receipt, tt.store)
			if got != tt.expected {
				t.Errorf("calculateFreeTrialDays() = %v, want %v", got, tt.expected)
			}
		})
	}
}
