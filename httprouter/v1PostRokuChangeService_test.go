package httprouter

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/foxcorp-product/commerce-purchase/config"
	"github.com/foxcorp-product/commerce-purchase/featureflag"
	"github.com/foxcorp-product/commerce-purchase/models"
	"github.com/foxcorp-product/commerce-purchase/utils"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/jwtdecoder"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/response"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"

	entitlement "github.com/foxcorp-product/commerce-entitlement/client"
	evergentproxy "github.com/foxcorp-product/commerce-evergentproxy/client"
	receiptverifyClient "github.com/foxcorp-product/commerce-purchase/clients/receiptverify"
	receiptverifyModels "github.com/foxcorp-product/commerce-purchase/clients/receiptverify/models"
)

func TestV1PostRokuChangeService(t *testing.T) {
	stat, err := stats.New("TestV1PostRokuChangeService", "v1", stats.WithDevMode())
	require.NoError(t, err)

	log, err := logger.New(logger.WithLogLevel("info"), logger.WithoutTimestamp())
	require.NoError(t, err)

	mockEvergentProxyClientOk := newEvergentProxyMock()
	mockEvergentProxyClientOk.Mock.On("SearchUser", mock.Anything, mock.Anything).Return(
		evergentproxy.V1SearchUserOutput{
			Found: true,
		}, nil)
	mockEvergentProxyClientOk.Mock.On("AddSubscription", mock.Anything, mock.Anything).Return(
		evergentproxy.V1AddSubscriptionOutput{}, nil)

	mockEvergentProxyClientErr := newEvergentProxyMock()
	mockEvergentProxyClientErr.Mock.On("SearchUser", mock.Anything, mock.Anything).Return(
		evergentproxy.V1SearchUserOutput{
			Found: true,
		}, nil)
	mockEvergentProxyClientErr.Mock.On("AddSubscription", mock.Anything, mock.Anything).Return(
		evergentproxy.V1AddSubscriptionOutput{}, errors.New("error"))

	for name, tt := range map[string]struct {
		request            models.V1PostRokuChangeServiceRequest
		deps               V1PurchaseDependency
		expectedStatusCode int
		expectedResponse   any
	}{
		"fail: failed to validate the Roku's receipt": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return nil, http.StatusInternalServerError, errors.New("failed validate receipt")
						},
					},
				},
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse: response.MakeFromError(http.StatusInternalServerError, []error{
				errors.New("error validating roku receipt: failed validate receipt"),
			}),
		},
		"fail: no cancelledTransactionIds on the Roku's receipt": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								CancelledTransactionIds: nil,
							}, http.StatusOK, nil
						},
					},
				},
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse: response.MakeFromError(http.StatusInternalServerError, []error{
				errors.New("incorrect number of cancelled transactions in receipt"),
			}),
		},
		"fail: failed to find the current purchase": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								TransactionId:           utils.ToPtr("NewTransactionId"),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return nil, errors.New("transaction not found")
					},
				},
			},
			expectedStatusCode: http.StatusNotFound,
			expectedResponse: response.MakeFromError(http.StatusNotFound, []error{
				errors.New("error finding transaction: roku-cancelled-transaction-id, NewTransactionId, transaction not found"),
			}),
		}, "fail: error trying to validate service change": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(nil, errors.New("error validating service change"))
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
				},
			},
			expectedStatusCode: http.StatusBadRequest,
			expectedResponse: response.MakeFromError(http.StatusBadRequest, []error{
				errors.New("error validating service change: error validating service change"),
			}),
		}, "fail: failed to validate service change": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: "Ineligible",
						}, nil)
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
				},
			},
			expectedStatusCode: http.StatusBadRequest,
			expectedResponse: response.MakeFromError(http.StatusBadRequest, []error{
				errors.New("error validating service change: Ineligible"),
			}),
		},
		"fail: failed to renew the entitlement": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingUpgrade,
						}, nil)
						m.On("PutEntitlementRenew", mock.Anything, mock.Anything).Return(nil, errors.New("entitlement not found"))
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
				},
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse: response.MakeFromError(http.StatusInternalServerError, []error{
				errors.New("error updating entitlement: entitlement not found"),
			}),
		},
		"fail: failed to get entitlement": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingDowngrade,
						}, nil)
						m.On("GetS2sEntitlementsUidAppServiceId", mock.Anything, mock.Anything).Return(nil, errors.New("entitlement not found"))
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
				},
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse: response.MakeFromError(http.StatusInternalServerError, []error{
				errors.New("error getting entitlement: entitlement not found"),
			}),
		},
		"fail: error setting pending service change": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingDowngrade,
						}, nil)
						m.On("GetS2sEntitlementsUidAppServiceId", mock.Anything, mock.Anything).Return(&entitlement.V1GetEntitlemenResponse{
							Entitlement: &entitlement.V1EntitlementObject{},
						}, nil)
						m.On("S2SSetPendingServiceChange", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error updating status change"))
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
				},
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse: response.MakeFromError(http.StatusInternalServerError, []error{
				errors.New("error setting pending service change: error updating status change"),
			}),
		},
		"fail: failed to update the purchase's receipt": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingUpgrade,
						}, nil)
						m.On("PutEntitlementRenew", mock.Anything, mock.Anything).Return(&entitlement.V1PutEntitlemenRenewResponse{}, nil)
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
					updateReceipt: func(context.Context, string, models.V1StoreLabel, string, string, string, string, bool) error {
						return errors.New("failed to update the purchase's receipt")
					},
				},
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse: response.MakeFromError(http.StatusInternalServerError, []error{
				errors.New("error updating receipt: failed to update the purchase's receipt"),
			}),
		},
		"success: IDENPOTENCY": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
								TransactionId:           utils.ToPtr("NewTransactionId"),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return nil, errors.New("transaction not found")
					},
					findTransaction2: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:           "purchaseId",
							Uid:          "userId",
							AppServiceId: "appServiceId",
							Receipt:      "NewTransactionId",
						}, nil
					},
					updateReceipt: func(context.Context, string, models.V1StoreLabel, string, string, string, string, bool) error {
						return nil
					},
				},
			},
			expectedStatusCode: http.StatusOK,
			expectedResponse: models.V1PostRokuChangeServiceResponse{
				PurchaseId: "purchaseId",
				Uid:        "userId",
			},
		},
		"success: UPGRADE": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingUpgrade,
						}, nil)
						m.On("PutEntitlementRenew", mock.Anything, mock.Anything).Return(&entitlement.V1PutEntitlemenRenewResponse{}, nil)
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
					updateReceipt: func(context.Context, string, models.V1StoreLabel, string, string, string, string, bool) error {
						return nil
					},
				},
			},
			expectedStatusCode: http.StatusOK,
			expectedResponse: models.V1PostRokuChangeServiceResponse{
				PurchaseId: "purchaseId",
				Uid:        "userId",
			},
		},
		"success: DOWNGRADE": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				clients: Clients{
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingDowngrade,
						}, nil)
						m.On("GetS2sEntitlementsUidAppServiceId", mock.Anything, mock.Anything).Return(&entitlement.V1GetEntitlemenResponse{
							Entitlement: &entitlement.V1EntitlementObject{},
						}, nil)
						m.On("S2SSetPendingServiceChange", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1SetPendingServiceChangeResponse{}, nil)
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
					updateReceipt: func(context.Context, string, models.V1StoreLabel, string, string, string, string, bool) error {
						return nil
					},
				},
			},
			expectedStatusCode: http.StatusOK,
			expectedResponse: models.V1PostRokuChangeServiceResponse{
				PurchaseId: "purchaseId",
				Uid:        "userId",
			},
		},
		"success: Forward to Evergent": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "true",
				},
				utils: Utils{
					purchaseIDCreator: utils.PurchaseIDCreator,
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{},
					S: map[string]string{
						"allow_bypass_store_list": "rokustore",
					},
				},
				clients: Clients{
					evergentproxy: mockEvergentProxyClientOk,
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingDowngrade,
						}, nil)
						m.On("GetS2sEntitlementsUidAppServiceId", mock.Anything, mock.Anything).Return(&entitlement.V1GetEntitlemenResponse{
							Entitlement: &entitlement.V1EntitlementObject{},
						}, nil)
						m.On("S2SSetPendingServiceChange", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1SetPendingServiceChangeResponse{}, nil)
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
					updateReceipt: func(context.Context, string, models.V1StoreLabel, string, string, string, string, bool) error {
						return nil
					},
				},
			},
			expectedStatusCode: http.StatusOK,
			expectedResponse: models.V1PostRokuChangeServiceResponse{
				PurchaseId: "purchaseId",
				Uid:        "userId",
			},
		},
		"success: Forward to Evergent is skipped for D2C": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "true",
				},
				utils: Utils{
					purchaseIDCreator: utils.PurchaseIDCreator,
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{},
					S: map[string]string{
						"allow_bypass_store_list": "rokustore",
					},
				},
				clients: Clients{
					evergentproxy: mockEvergentProxyClientOk,
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingDowngrade,
						}, nil)
						m.On("GetS2sEntitlementsUidAppServiceId", mock.Anything, mock.Anything).Return(&entitlement.V1GetEntitlemenResponse{
							Entitlement: &entitlement.V1EntitlementObject{},
						}, nil)
						m.On("S2SSetPendingServiceChange", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1SetPendingServiceChangeResponse{}, nil)
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:    "purchaseId",
							Uid:   "userId",
							AppId: "d2c",
						}, nil
					},
					updateReceipt: func(context.Context, string, models.V1StoreLabel, string, string, string, string, bool) error {
						return nil
					},
				},
			},
			expectedStatusCode: http.StatusOK,
			expectedResponse: models.V1PostRokuChangeServiceResponse{
				PurchaseId: "purchaseId",
				Uid:        "userId",
			},
		},
		"error: Forward to Evergent": {
			request: models.V1PostRokuChangeServiceRequest{
				AppServiceId: "appServiceId",
				Receipt:      "receipt",
			},
			deps: V1PurchaseDependency{
				cfg: &config.Config{
					UseEvergent: "true",
				},
				utils: Utils{
					purchaseIDCreator: utils.PurchaseIDCreator,
				},
				featureClient: &featureflag.FeatureMockClient{B: map[string]bool{},
					S: map[string]string{
						"allow_bypass_store_list": "rokustore",
					},
				},
				clients: Clients{
					evergentproxy: mockEvergentProxyClientErr,
					receiptVerify: mockReceiptVerify{
						postReceiptverifyValidateRokuTransaction: func(
							context.Context,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody,
							receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams,
						) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, int, error) {
							return &receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse{
								StartDate:               utils.ToPtr(time.Now()),
								CancelledTransactionIds: utils.ToPtr([]string{"roku-cancelled-transaction-id"}),
							}, http.StatusOK, nil
						},
					},
					entitlements: func() EntitlementClient {
						m := newEntitlementMock()
						m.On("GetValidateServiceChange", mock.Anything, mock.Anything).Return(&entitlement.V1GetValidateServiceChangeResponse{
							Status: PendingDowngrade,
						}, nil)
						m.On("GetS2sEntitlementsUidAppServiceId", mock.Anything, mock.Anything).Return(&entitlement.V1GetEntitlemenResponse{
							Entitlement: &entitlement.V1EntitlementObject{},
						}, nil)
						m.On("S2SSetPendingServiceChange", mock.Anything, mock.Anything, mock.Anything).Return(&entitlement.V1SetPendingServiceChangeResponse{}, nil)
						return m
					}(),
				},
				purchaseSvc: &mockPurchaseSvc{
					findTransaction: func(context.Context, models.V1StoreLabel, string, bool) (*models.V1Purchase, error) {
						return &models.V1Purchase{
							Id:  "purchaseId",
							Uid: "userId",
						}, nil
					},
					updateReceipt: func(context.Context, string, models.V1StoreLabel, string, string, string, string, bool) error {
						return nil
					},
				},
			},
			expectedStatusCode: http.StatusFailedDependency,
		},
	} {
		t.Run(name, func(t *testing.T) {
			body, err := json.Marshal(tt.request)
			require.NoError(t, err)

			ctx := context.Background()
			rh := request.GetFromContext(ctx)
			rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "userId"})
			rh.SetAPIKey(apikeys.APIKey{APIKey: "APIKey"})
			ctx = request.NewContext(ctx, rh)

			r := httptest.NewRequestWithContext(ctx, http.MethodPost, "/purchase/roku/change-service", bytes.NewBuffer(body))
			rw := httptest.NewRecorder()

			tt.deps.stat = stat
			tt.deps.log = log
			h := newHandler(&tt.deps)

			router := chi.NewRouter()
			router.
				With(PostRokuChangeServiceInputValidator(route.Route{})).
				Post("/purchase/roku/change-service", h.V1PostRokuChangeService)
			router.ServeHTTP(rw, r)

			assert.Equal(t, tt.expectedStatusCode, rw.Code)

			if tt.expectedStatusCode == http.StatusOK {
				response, err := json.Marshal(tt.expectedResponse)
				require.NoError(t, err)
				assert.Equal(t, string(response)+"\n", rw.Body.String())
			}
		})
	}
}

func TestV1RokuChangeServiceNoInputValidator(t *testing.T) {
	r := httptest.NewRequest(http.MethodPost, "/purchase/roku/change-service", nil)
	rw := httptest.NewRecorder()

	stat, err := stats.New("TestV1RokuChangeService", "v1", stats.WithDevMode())
	require.NoError(t, err)

	log, err := logger.New(logger.WithLogLevel("info"), logger.WithoutTimestamp())
	require.NoError(t, err)

	h := newHandler(&V1PurchaseDependency{stat: stat, log: log})

	router := chi.NewRouter()
	router.Post("/purchase/roku/change-service", h.V1PostRokuChangeService)
	router.ServeHTTP(rw, r)

	assert.Equal(t, http.StatusInternalServerError, rw.Code)

	want := response.MakeFromError(http.StatusInternalServerError, []error{errors.New("request input not found")})
	expectedBody, err := json.Marshal(want)
	require.NoError(t, err)
	assert.Equal(t, string(expectedBody)+"\n", rw.Body.String())
}
