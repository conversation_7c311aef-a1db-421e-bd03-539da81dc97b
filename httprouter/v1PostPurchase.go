package httprouter

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/foxcorp-product/commerce-purchase/models"
	"github.com/foxcorp-product/commerce-purchase/purchase"
	"github.com/foxcorp-product/commerce-purchase/utils"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/jwtdecoder"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/response"
	"github.com/foxcorp-product/entitlement-sdk/stats"

	entitlement "github.com/foxcorp-product/commerce-entitlement/client"
	publisher "github.com/foxcorp-product/commerce-notificationpublisher/client"
	accountV2 "github.com/foxcorp-product/commerce-purchase/clients/accountV2"
	receiptverify "github.com/foxcorp-product/commerce-purchase/clients/receiptverify"
	receiptverifyModels "github.com/foxcorp-product/commerce-purchase/clients/receiptverify/models"
)

type PostPurchaseInput struct {
	models.V1PostPurchaseRequest `in:"body=json" validate:"required"`
}

var PostPurchaseInputValidator = inputvalidator.MakeRouteValidatorMiddleware[PostPurchaseInput](
	inputvalidator.WithValidator("valid_store", ValidStore),
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithoutErrorWrap(),
)

const (
	AlreadySubscribedEvergentErrorCode = "eV4050"
)

func (h V1PurchaseHandler) verifyReceipt(ctx context.Context, req *models.V1PostPurchaseRequest, apikey string) (*receiptverifyModels.V1PostReceiptverifyResponse, int, error) {
	if utils.FromPtr(req.IsAppstoreTransaction) {
		body := receiptverify.PostReceiptverifyValidateAppstoreTransactionBody{
			AppServiceId: req.AppServiceId,
			Receipt:      req.Receipt,
		}
		if req.AllowExpired != nil && *req.AllowExpired {
			body.AllowExpired = *req.AllowExpired
		}
		return h.d.clients.receiptVerify.PostReceiptverifyValidateAppstoreTransaction(
			ctx,
			body,
			receiptverify.PostReceiptverifyValidateAppstoreTransactionHeaderParams{
				XApiKey: &apikey,
			},
		)
	}

	return h.d.clients.receiptVerify.PostReceiptverify(
		ctx,
		receiptverify.PostReceiptverifyBody{
			AllowExpired:   req.AllowExpired,
			AppServiceId:   req.AppServiceId,
			PlatformUserId: req.PlatformUserId,
			Receipt:        req.Receipt,
		},
		receiptverify.PostReceiptverifyHeaderParams{
			XApiKey: apikey,
		},
	)
}

func buildSetPurchase(ctx context.Context, req *models.V1PostPurchaseRequest, uid, appId, pid, purchaseId, transactionId string, nextCheckDate *time.Time, validReceipt, validTransaction bool, receipt *receiptverifyModels.V1PostReceiptverifyResponse) purchase.V1SetPurchase {
	setPurchase := purchase.V1SetPurchase{
		Uid:                   uid,
		Pid:                   pid,
		Store:                 models.V1StoreLabel(req.Label),
		PurchaseId:            purchaseId,
		AppId:                 appId,
		AppServiceID:          req.AppServiceId,
		Receipt:               req.Receipt,
		TransactionId:         transactionId,
		PlatformUserId:        req.PlatformUserId,
		RokuPucId:             req.RokuPucId,
		NextCheckDate:         nextCheckDate,
		ValidReceipt:          validReceipt,
		ValidTransaction:      validTransaction,
		IsAppstoreTransaction: utils.FromPtr(req.IsAppstoreTransaction),
	}

	if setPurchase.Store == models.V1StoreLabelPlaystore {
		setPurchase.PurchaseToken = utils.ToPtr(getPlaystorePurchaseTokenFromEncodedReceipt(ctx, req.Receipt))
	}

	if receipt != nil {
		setPurchase.FreeTrialDays = calculateFreeTrialDays(receipt, setPurchase.Store)
	}

	return setPurchase
}

func getNextCheckDateForSamsung(rvr *receiptverifyModels.V1PostReceiptverifyResponse, samsungPurchaseRecordTime *time.Time) (*time.Time, error) {
	if rvr.Store != "tizen" {
		return nil, nil
	}

	if rvr.NextPaymentDate == nil || rvr.NextPaymentDate.IsZero() {
		return nil, errors.New("next payment date is empty")
	}

	if samsungPurchaseRecordTime == nil || samsungPurchaseRecordTime.IsZero() {
		return rvr.NextPaymentDate, nil
	}

	if samsungPurchaseRecordTime.Before(*rvr.NextPaymentDate) {
		return samsungPurchaseRecordTime, nil
	}

	return rvr.NextPaymentDate, nil
}

func buildEntitlementChargedFromReceiptVerify(charged *receiptverifyModels.V1Charged, chargedAmount float64) *entitlement.V1EntitlementCharged {
	currency := "USD"
	date := time.Now()

	if charged != nil {
		currency = charged.Currency
		date = charged.Date
	}

	return &entitlement.V1EntitlementCharged{
		Amount:   chargedAmount,
		Currency: currency,
		Date:     date,
	}
}

func (h V1PurchaseHandler) V1PostPurchase(w http.ResponseWriter, r *http.Request) {
	var (
		err  error
		span stats.Span
		ctx  = r.Context()
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, "v1PurchaseHandler.V1Purchase")
	defer func() { span.FinishWithError(err) }()

	rl := request.GetFromContext(ctx).GetLoggingEntry()
	rh := request.GetFromContext(r.Context())
	jwtClaims := rh.GetJWTClaims()
	if jwtClaims == nil {
		rl.Errorf("error calling rh.GetJWTClaims(): %v", err)
		err = utils.ErrFieldRequired(fmt.Sprintf("header.%s OR header.%s for claims", jwtdecoder.AccessTokenHeader, jwtdecoder.AuthorizationHeader))
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
		return
	}

	uid := jwtClaims.Uid
	rl = rl.WithField("uid", uid)
	span.SetTag("input.uid", uid)
	if strings.TrimSpace(uid) == "" {
		sendError(ctx, w, "V1PurchaseHandler.V1Purchase", errMissingUid)
		return
	}

	input, ok := inputvalidator.GetValidatedStruct[PostPurchaseInput](r.Context())
	if !ok {
		err = errors.New("request input not found")
		rl.Errorf("error while getting the request input: %s", err.Error())
		response.SendErrors(w, http.StatusBadRequest, err)
		return
	}

	rl = rl.WithField("PlatformUserId", input.PlatformUserId)
	span.SetTag("input.platformUserId", input.PlatformUserId)
	rl = rl.WithField("AppServiceId", input.AppServiceId)
	span.SetTag("input.appServiceId", input.AppServiceId)

	rl.Infof("Purchase request received")

	pid := jwtClaims.Pid
	rl = rl.WithField("pid", pid)
	span.SetTag("input.pid", pid)

	apiKey := rh.GetAPIKey()
	if apiKey.APIKey == "" {
		err = utils.ErrFieldRequired("header." + request.APIKeyHeader)
		rl.Errorf("error getting api key: %s", err.Error())
		sendError(ctx, w, "v1PurchaseHandler.AllowValidUser", err)
		return
	}

	pathParams := accountV2.GetUserDetailsPathParams{
		Uid: uid,
	}
	headerParams := accountV2.GetUserDetailsHeaderParams{
		XApigwApiId:  &h.d.cfg.VPCKeyCommIdentity,
		XAccessToken: fmt.Sprintf("Bearer %s", rh.GetJWTToken()),
	}
	/*call identity service to get email and name*/
	userRecord, _, err := h.d.clients.accountV2.GetUserDetails(ctx, pathParams, headerParams)
	if err != nil {
		rl.Errorf("error calling accountV2.GetUserDetails: %v", err)
		sendError(ctx, w, "V1PurchaseHandler.V1Purchase", err)
		return
	}

	if !userRecord.HasEmail {
		rl.Errorf("error calling serRecord.HasEmail: %v", err)
		sendError(ctx, w, "V1PurchaseHandler.V1Purchase", errMissingUserEmail)
		return
	}

	// this is the index of the table and the purchaseId for the transaction
	purchaseId := h.d.utils.purchaseIDCreator()
	rl = rl.WithField("purchaseId", purchaseId)
	span.SetTag("input.purchaseId", purchaseId)

	appId := apiKey.Brand

	rl.Debugf("creating new v1SetPurchase for store %s with id %s, is apple transaction %t ", input.Label, purchaseId, utils.FromPtr(input.IsAppstoreTransaction))

	// call to ReceiptVerify service
	rvr, code, err := h.verifyReceipt(ctx, &input.V1PostPurchaseRequest, apiKey.APIKey)
	if err != nil {
		rl.Errorf("error calling receipt verify: %v", err)
		err := utils.WrapHttpError(code, err)
		if code == http.StatusInternalServerError || code == http.StatusFailedDependency {
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase.verifyReceipt", err)
			return
		}

		// save the receipt in the database with the corresponding status
		if _, err := h.d.purchaseSvc.Set(ctx, buildSetPurchase(ctx, &input.V1PostPurchaseRequest, uid, appId, pid, purchaseId, "", &time.Time{}, false, false, rvr)); err != nil {
			rl.Errorf("error calling purchaseSvc.Set: %v", err)
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase.purchaseSvcSet", err)
			return
		}

		sendError(ctx, w, "v1PurchaseHandler.V1Purchase.verifyReceipt", err)

		return
	}

	purchaseRecord, err := h.d.purchaseSvc.FindTransaction(ctx, models.V1StoreLabel(rvr.Store), rvr.TransactionId, true)
	if err != nil && !utils.ErrIsNotFound(err) {
		rl.Errorf("error calling purchaseSvc.FindTransaction: %v", err)
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
		return
	}
	purchaseExists := purchaseRecord != nil
	rl = rl.WithField("transactionId", rvr.TransactionId)
	span.SetTag("input.transactionId", rvr.TransactionId)
	span.SetTag("input.brand", apiKey.Brand)

	// might have to check if uid is nil here or not
	if purchaseExists && purchaseRecord.Uid != uid {
		rl.Errorf("receipt belongs to a different user")
		receiptErr := utils.NewErrBadRequest(fmt.Errorf("receipt with transaction id %s belongs to a different user", rvr.TransactionId))
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", receiptErr)
		return
	}
	samsungPurchaseCheckDate := new(time.Time)
	if purchaseExists && rvr.Store == "tizen" {
		samsungPurchaseRecord, err := h.d.purchaseSvc.GetSamsungPurchaseByReceipt(ctx, purchaseRecord.Id)
		if err != nil && !errors.Is(err, utils.ErrEntryNotFound) {
			rl.Errorf("error retrieving samsung v1SetPurchase receipt: %v", err)
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
			return
		}
		// If Not Found, then samsungPurchaseRecord will be nil
		if samsungPurchaseRecord != nil {
			samsungPurchaseCheckDate = &samsungPurchaseRecord.NextCheckDate
		}
	}

	nextCheckDate, err := getNextCheckDateForSamsung(rvr, samsungPurchaseCheckDate)
	if err != nil {
		rl.Errorf("error calling getNextCheckDateForSamsung: %v", err)
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
		return
	}

	token := rh.GetJWTToken()
	if token == "" {
		err := utils.ErrFieldRequired(fmt.Sprintf("header.%s OR header.%s", jwtdecoder.AccessTokenHeader, jwtdecoder.AuthorizationHeader))
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
		return
	}

	chargedAmount := selectChargedAmount(rvr.Charged, input.PriceCharged)
	formatedChargedAmount := fmt.Sprintf("%.2f", chargedAmount)

	// v1SetPurchase already exist, must be a resubscribed transaction
	// handle as renew, do not create another v1SetPurchase record
	if purchaseRecord != nil {
		entitlementRenewBody := entitlement.PutEntitlementRenewBody{
			AppServiceId: input.AppServiceId,
			Charged:      buildEntitlementChargedFromReceiptVerify(rvr.Charged, chargedAmount),
			FreeTrial:    utils.FromPtr(rvr.FreeTrial),
			StartDate:    rvr.StartDate, // do
			Uid:          uid,
			EndDate:      rvr.EndDate,
		}

		if rvr.FlexibleOfferPrice != "" && rvr.Charged != nil {
			entitlementRenewBody.Charged.Amount, err = strconv.ParseFloat(rvr.FlexibleOfferPrice, 64)
			if err != nil {
				sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
				return
			}
		}

		resp, err := h.d.clients.entitlements.PutEntitlementRenew(ctx, entitlementRenewBody)
		if err != nil {
			rl.Errorf("error calling entitlements.PutEntitlementRenew: %v", err)
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
			return
		}

		isUseEvergent, err := h.d.GetUseEvergent(ctx)
		if err != nil {
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
			return
		}

		evBypassInput := EvergentBypassInput{
			AccessToken:    token,
			UID:            uid,
			PurchaseID:     purchaseId,
			Label:          rvr.Store.String(),
			AppServiceID:   input.AppServiceId,
			PlatformUserID: input.PlatformUserId,
			PriceCharged:   &formatedChargedAmount,
			RokuPucID:      input.RokuPucId,
			Receipt:        input.Receipt,
		}
		if err = h.forwardPurchaseToEvergent(
			ctx,
			rl,
			isUseEvergent,
			appId,
			evBypassInput,
		); err != nil && IsNotSubscribedToEvergentError(err) {
			rl.Errorf("error calling forwardPurchaseToEvergent: %v", err)
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
			return
		}

		err = h.forwardPurchaseConfirmation(ctx, models.V1StoreLabel(input.Label), rvr.FreeTrial, purchaseRecord, rvr)
		if err != nil {
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase", utils.WrapHttpError(http.StatusFailedDependency, err))
			return
		}

		err = h.notifyPnR(ctx, publisher.PnRNotificationTypePurchaseRenew, rvr.Store.String(), uid, input.AppServiceId)
		if err != nil {
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase", utils.WrapHttpError(http.StatusFailedDependency, err))
			return
		}

		rl.Infof("resubscribe success for uid %s, v1SetPurchase id %s, appServiceId %s", uid, purchaseId, input.AppServiceId)
		sendJSONResponse(w, r, resp.Entitlement, http.StatusOK)
		return
	}

	postEntitlementBody := entitlement.PostEntitlementBody{
		AppServiceId:  input.AppServiceId,
		Charged:       buildEntitlementChargedFromReceiptVerify(rvr.Charged, chargedAmount),
		StartDate:     rvr.StartDate,
		EndDate:       rvr.EndDate,
		FreeTrial:     utils.FromPtr(rvr.FreeTrial),
		PurchaseId:    purchaseId,
		PaymentMethod: input.Label,
	}
	if rvr.FlexibleOfferPrice != "" && rvr.Charged != nil {
		postEntitlementBody.Charged.Amount, err = strconv.ParseFloat(rvr.FlexibleOfferPrice, 64)
		if err != nil {
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
			return
		}
	}
	// call to Entitlements service
	entitlementResponse, err := h.d.clients.entitlements.PostEntitlement(
		ctx,
		postEntitlementBody,
		entitlement.PostEntitlementHeaderParams{
			XApiKey:      apiKey.APIKey,
			XAccessToken: token,
		},
	)
	if err != nil {
		rl.WithFields(logger.Fields{"Uid": uid}).Errorf("error calling entitlements.PostEntitlement: %v", err)
		// save the receipt in the database with the corresponding status
		if _, err := h.d.purchaseSvc.Set(ctx, buildSetPurchase(ctx, &input.V1PostPurchaseRequest, uid, appId, pid, purchaseId, rvr.TransactionId, nextCheckDate, true, false, rvr)); err != nil {
			sendError(ctx, w, "v1PurchaseHandler.V1Purchase", utils.WrapHttpError(http.StatusFailedDependency, err))
			return
		}
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", utils.WrapHttpError(http.StatusFailedDependency, err))
		return
	}

	// save the receipt in the database with the corresponding status
	v1SetPurchase := buildSetPurchase(ctx, &input.V1PostPurchaseRequest, uid, appId, pid, purchaseId, rvr.TransactionId, nextCheckDate, true, true, rvr)
	if _, err = h.d.purchaseSvc.Set(ctx, v1SetPurchase); err != nil {
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
		return
	}

	isUseEvergent, err := h.d.GetUseEvergent(ctx)
	if err != nil {
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", err)
		return
	}

	evBypassInput := EvergentBypassInput{
		AccessToken:    token,
		UID:            uid,
		PurchaseID:     purchaseId,
		Label:          rvr.Store.String(),
		AppServiceID:   input.AppServiceId,
		PlatformUserID: input.PlatformUserId,
		PriceCharged:   &formatedChargedAmount,
		RokuPucID:      input.RokuPucId,
		Receipt:        input.Receipt,
	}
	if err = h.forwardPurchaseToEvergent(ctx, rl, isUseEvergent, appId, evBypassInput); err != nil {
		rl.Errorf("error calling forwardPurchaseToEvergent: %v", err)
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", utils.WrapHttpError(http.StatusFailedDependency, err))
		return
	}

	purchaseRecord = &models.V1Purchase{
		AppServiceId:   v1SetPurchase.AppServiceID,
		Id:             v1SetPurchase.PurchaseId,
		Platform:       rvr.Store.String(),
		PlatformUserId: v1SetPurchase.PlatformUserId,
		RokuPucId:      v1SetPurchase.RokuPucId,
		Receipt:        v1SetPurchase.Receipt,
		Uid:            v1SetPurchase.Uid,
	}

	err = h.forwardPurchaseConfirmation(ctx, models.V1StoreLabel(input.Label), rvr.FreeTrial, purchaseRecord, rvr)
	if err != nil {
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", utils.WrapHttpError(http.StatusFailedDependency, err))
		return
	}

	err = h.notifyPnR(ctx, publisher.PnRNotificationTypePurchaseConfirmation, rvr.Store.String(), uid, input.AppServiceId)
	if err != nil {
		sendError(ctx, w, "v1PurchaseHandler.V1Purchase", utils.WrapHttpError(http.StatusFailedDependency, err))
		return
	}

	rl.Infof("Purchase completed successfully")
	sendJSONResponse(w, r, entitlementResponse.Entitlement, http.StatusOK)
}

func (h V1PurchaseHandler) forwardPurchaseToEvergent(ctx context.Context, rl logger.Logger, useEvergent bool, brand string, evBypassInput EvergentBypassInput) error {
	// Call evergent:https://teamfox.atlassian.net/browse/CSPSUBS-547
	// validate useEvergent consul variable and allowByPassStoresList="tizen,playstore,etc.."
	if useEvergent && strings.Contains(h.d.GetAllowBypassStoresList(ctx), evBypassInput.Label) && strings.EqualFold(brand, "foxnation") {
		rl.Infof(
			"calling evergent bypass is enabled for store %s. Forwarding uid %s, purchase id %s, appServiceId %s",
			evBypassInput.Label,
			evBypassInput.UID,
			evBypassInput.PurchaseID,
			evBypassInput.AppServiceID,
		)
		return h.callEvergentBypass(ctx, evBypassInput)
	}
	return nil
}

func IsNotSubscribedToEvergentError(addSubError error) bool {
	if addSubError == nil {
		return true
	}
	if strings.Contains(addSubError.Error(), AlreadySubscribedEvergentErrorCode) {
		return false
	}
	return true
}

// getPlaystorePurchaseTokenFromEncodedReceipt base64 decode the receipt and get the purchaseToken field
func getPlaystorePurchaseTokenFromEncodedReceipt(ctx context.Context, receipt string) string {
	rl := request.GetFromContext(ctx).GetLoggingEntry()

	decodedReceipt, err := base64.StdEncoding.DecodeString(receipt)
	if err != nil {
		rl.Errorf("error base64 decoding receipt on getPlaystorePurchaseTokenFromEncodedReceipt: %v", err)
		return ""
	}

	var receiptData map[string]interface{}
	err = json.Unmarshal(decodedReceipt, &receiptData)
	if err != nil {
		rl.Errorf("error unmarshalling receipt on getPlaystorePurchaseTokenFromEncodedReceipt: %v", err)
		return ""
	}

	purchaseToken, ok := receiptData["purchaseToken"].(string)
	if !ok {
		rl.Errorf("error retrieving purchaseToken on getPlaystorePurchaseTokenFromEncodedReceipt: %v", err)
		return ""
	}

	return purchaseToken
}

func calculateFreeTrialDays(receipt *receiptverifyModels.V1PostReceiptverifyResponse, store models.V1StoreLabel) int64 {
	if receipt.FreeTrial == nil || !*receipt.FreeTrial {
		return 0
	}

	if store == models.V1StoreLabelTizen {
		return int64(receipt.NextPaymentDate.Sub(receipt.StartDate).Hours() / 24)
	}

	return int64(receipt.EndDate.Sub(receipt.StartDate).Hours() / 24)
}

// In case the receipt does not return a charged amount, default to the input charged amount
func selectChargedAmount(rvrCharged *receiptverifyModels.V1Charged, inputPriceCharged *float64) float64 {
	if inputPriceCharged != nil && *inputPriceCharged != 0 {
		return *inputPriceCharged
	}
	if rvrCharged != nil && rvrCharged.Amount != 0 {
		return rvrCharged.Amount
	}
	return 0
}
