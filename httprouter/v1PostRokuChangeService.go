package httprouter

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/foxcorp-product/commerce-purchase/models"
	"github.com/foxcorp-product/commerce-purchase/utils"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/response"
	"github.com/foxcorp-product/entitlement-sdk/stats"

	entitlement "github.com/foxcorp-product/commerce-entitlement/client"
	entitlementService "github.com/foxcorp-product/commerce-entitlement/entitlement"
	receiptverifyClient "github.com/foxcorp-product/commerce-purchase/clients/receiptverify"
	receiptverifyModels "github.com/foxcorp-product/commerce-purchase/clients/receiptverify/models"
)

type PostRokuChangeServiceInput struct {
	models.V1PostRokuChangeServiceRequest `in:"body=json" validate:"required"`
}

var PostRokuChangeServiceInputValidator = inputvalidator.MakeRouteValidatorMiddleware[PostRokuChangeServiceInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithoutErrorWrap(),
)

func (h V1PurchaseHandler) V1PostRokuChangeService(w http.ResponseWriter, r *http.Request) {
	var (
		err  error
		span stats.Span
		ctx  = r.Context()
		rh   = request.GetFromContext(ctx)
		rl   = rh.GetLoggingEntry()
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, "v1PurchaseHandler.V1PostRokuChangeService")
	defer func() { span.FinishWithError(err) }()
	r = r.WithContext(ctx)

	input, ok := inputvalidator.GetValidatedStruct[PostRokuChangeServiceInput](r.Context())
	if !ok {
		err = errors.New("request input not found")
		rl.Errorf("error while getting the request input: %s", err.Error())
		response.SendErrors(w, http.StatusInternalServerError, err)
		return
	}

	// validate the receipt (we need the start date)
	receipt, err := h.validateRokuReceipt(ctx, input.AppServiceId, input.Receipt)
	if err != nil {
		err = utils.NewErrInternalServerError(fmt.Errorf("error validating roku receipt: %w", err))
		sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", err)
		return
	}

	// expecting one and only one cancelled transaction
	cancelledTransactions := utils.FromPtr(receipt.CancelledTransactionIds)
	if len(cancelledTransactions) != 1 {
		err = utils.NewErrInternalServerError(fmt.Errorf("incorrect number of cancelled transactions in %s", input.Receipt))
		sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", err)
		return
	}
	oldTransactionID := cancelledTransactions[0]
	newTransactionID := utils.FromPtr(receipt.TransactionId)

	span.SetTag("input.old_transaction_id", oldTransactionID)
	span.SetTag("input.new_transaction_id", newTransactionID)

	// fetch the current purchase and validate
	purchase, err := h.d.purchaseSvc.FindTransaction(ctx, models.V1StoreLabelRokustore, oldTransactionID, true)
	if err != nil {
		// allow idenpotency if the transaction is already the new one, return the purchase
		if purchase, err := h.d.purchaseSvc.FindTransaction(ctx, models.V1StoreLabelRokustore, newTransactionID, true); err == nil {
			sendJSONResponse(w, r, models.V1PostRokuChangeServiceResponse{
				Uid:        purchase.Uid,
				PurchaseId: purchase.Id,
			}, http.StatusOK)
			return
		}

		err = utils.NewErrNotFound(fmt.Errorf("error finding transaction: %s, %s, %w", oldTransactionID, newTransactionID, err))
		sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", err)
		return
	}

	// Validate service change
	serviceChangeResp, err := h.d.clients.entitlements.GetValidateServiceChange(ctx, entitlement.GetValidateServiceChangeBody{
		Uid:            purchase.Uid,
		AppServiceId:   input.AppServiceId,
		CheckFreeTrial: true,
	})
	if err != nil {
		err = utils.NewErrBadRequest(fmt.Errorf("error validating service change: %w", err))
		sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", err)
		return
	}

	appServiceId := purchase.AppServiceId

	switch serviceChangeResp.Status {
	case PendingDowngrade:
		err = h.processRokuPendingStatusChange(ctx, input, purchase, receipt)
		if err != nil {
			sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", err)
			return
		}

	case PendingUpgrade:
		// update app service id to the new one
		appServiceId = input.AppServiceId

		// upgrades should renew current entitlement right away
		peInput := entitlement.PutEntitlementRenewBody{
			AppServiceId: input.AppServiceId,
			Uid:          purchase.Uid,
			StartDate:    *receipt.StartDate,
			EndDate:      receipt.EndDate,
			FreeTrial:    false,
		}
		if receipt.Charged != nil {
			peInput.Charged = &entitlement.V1EntitlementCharged{
				Amount:   receipt.Charged.Amount,
				Currency: receipt.Charged.Currency,
			}
			if receipt.Charged.Date != nil {
				peInput.Charged.Date = *receipt.Charged.Date
			}
		}
		_, err = h.d.clients.entitlements.PutEntitlementRenew(ctx, peInput)
		if err != nil {
			err = utils.NewErrInternalServerError(fmt.Errorf("error updating entitlement: %w", err))
			sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", err)
			return
		}

	default:
		err = utils.NewErrBadRequest(fmt.Errorf("error validating service change: %s", serviceChangeResp.Status))
		sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", err)
		return
	}

	// update the new transactionId on the current purchase
	err = h.d.purchaseSvc.UpdateReceipt(ctx, purchase.Uid, models.V1StoreLabelRokustore,
		appServiceId, newTransactionID, newTransactionID, "", true)
	if err != nil {
		err = utils.NewErrInternalServerError(fmt.Errorf("error updating receipt: %w", err))
		sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", err)
		return
	}

	isUseEvergent, err := h.d.GetUseEvergent(ctx)
	if err != nil {
		rl.Errorf("error while getting the evergent flag: %s", err.Error())
		sendError(ctx, w, "v1PurchaseHandler.ForwardToComm1AddSubscriptionsMiddleware", err)
		return
	}

	appID := purchase.AppId
	if appID == "" {
		appID = "foxnation"
	}

	if isUseEvergent {
		formatedChargedAmount := ""
		if receipt.Charged != nil {
			formatedChargedAmount = fmt.Sprintf("%.2f", receipt.Charged.Amount)
		}
		if err = h.forwardPurchaseToEvergent(ctx, rl, isUseEvergent, appID, EvergentBypassInput{
			AccessToken:  rh.GetJWTToken(),
			UID:          rh.GetJWTClaims().Uid,
			PurchaseID:   h.d.utils.purchaseIDCreator(),
			Label:        string(utils.RokuStore),
			AppServiceID: input.AppServiceId,
			PriceCharged: &formatedChargedAmount,
			RokuPucID:    &input.RokuPucId,
			Receipt:      input.Receipt,
		}); err != nil && IsNotSubscribedToEvergentError(err) {
			rl.Errorf("error calling forwardPurchaseToEvergent: %v", err)
			sendError(ctx, w, "v1PurchaseHandler.V1PostRokuChangeService", utils.WrapHttpError(http.StatusFailedDependency, err))
			return
		}
	}

	sendJSONResponse(w, r, models.V1PostRokuChangeServiceResponse{
		Uid:        purchase.Uid,
		PurchaseId: purchase.Id,
	}, http.StatusOK)
}

func (h V1PurchaseHandler) processRokuPendingStatusChange(ctx context.Context, input *PostRokuChangeServiceInput,
	purchase *models.V1Purchase, receipt *receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse) error {
	ent, err := h.d.clients.entitlements.GetS2sEntitlementsUidAppServiceId(ctx, entitlement.GetS2sEntitlementsUidAppServiceIdPathParams{
		Uid:          purchase.Uid,
		AppServiceId: purchase.GetActiveAppServiceId(),
	})
	if err != nil {
		return utils.NewErrInternalServerError(fmt.Errorf("error getting entitlement: %w", err))
	}

	startDate := ent.Entitlement.ValidityTill
	if startDate == nil {
		startDate = receipt.StartDate
	}

	// set the current entitlement with status pending downgrade service change
	_, err = h.d.clients.entitlements.S2SSetPendingServiceChange(ctx, entitlement.S2SSetPendingServiceChangePathParams{
		Uid:                 purchase.Uid,
		CurrentAppServiceId: purchase.GetActiveAppServiceId(),
	}, entitlement.S2SSetPendingServiceChangeBody{
		TargetAppServiceId: input.AppServiceId,
		StartDate:          startDate,
		Status:             entitlementService.EntitlementChangeTypeDowngrade,
	})
	if err != nil {
		return utils.NewErrInternalServerError(fmt.Errorf("error setting pending service change: %w", err))
	}

	return nil
}

func (h V1PurchaseHandler) validateRokuReceipt(ctx context.Context, appServiceID string, receipt string) (*receiptverifyModels.V1PostReceiptverifyValidateRokuTransactionResponse, error) {
	headers := receiptverifyClient.PostReceiptverifyValidateRokuTransactionHeaderParams{}
	validateRokuReq := receiptverifyClient.PostReceiptverifyValidateRokuTransactionBody{
		AppServiceId:  appServiceID,
		TransactionId: receipt,
	}

	rvr, _, err := h.d.clients.receiptVerify.PostReceiptverifyValidateRokuTransaction(ctx, validateRokuReq, headers)
	if err != nil {
		return nil, err
	}

	return rvr, nil
}
